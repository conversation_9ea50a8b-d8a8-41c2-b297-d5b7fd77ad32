"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { User, Edit, Save, Upload, Camera, X, Plus } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";

interface Photo {
  id: string;
  url: string;
  key: string;
}

interface Profile {
  id: string;
  name: string;
  email: string;
  phoneNumber?: string;
  image?: string;
  dateOfBirth?: string;
  photos: Photo[];
  gender?: string;
  religion?: string;
  occupation?: string;
  isSmoker: boolean;
  acceptDifferentReligion: boolean;
  about?: string;
  psychTestCompleted: boolean;
  status: string;
}

export default function ProfilePage() {
  const [editing, setEditing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [uploadingPhotos, setUploadingPhotos] = useState(false);
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const photosInputRef = useRef<HTMLInputElement>(null);
  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    try {
      const token = localStorage.getItem('pairsona_token');
      if (!token) {
        router.replace('/login');
        return;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('pairsona_token');
          router.replace('/login');
          return;
        }
        throw new Error('Failed to fetch profile');
      }

      const data = await response.json();
      setProfile(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!profile) return;

    try {
      setSaving(true);
      const token = localStorage.getItem('pairsona_token');
      if (!token) {
        router.replace('/login');
        return;
      }

      const updateData = {
        name: profile.name,
        gender: profile.gender,
        religion: profile.religion?.toLowerCase(),
        occupation: profile.occupation,
        isSmoker: profile.isSmoker,
        acceptDifferentReligion: profile.acceptDifferentReligion,
        about: profile.about,
        dateOfBirth: profile.dateOfBirth,
      };

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/profile`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        throw new Error('Failed to update profile');
      }

      const updatedProfile = await response.json();
      setProfile(updatedProfile);
      setEditing(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save profile');
    } finally {
      setSaving(false);
    }
  };

  const handleImageUpload = async (file: File) => {
    try {
      setUploadingImage(true);
      const token = localStorage.getItem('pairsona_token');
      if (!token) return;

      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/profile/image`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload image');
      }

      const result = await response.json();
      if (profile) {
        setProfile({ ...profile, image: result.url });
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to upload image');
    } finally {
      setUploadingImage(false);
    }
  };

  const handlePhotosUpload = async (files: FileList) => {
    try {
      setUploadingPhotos(true);
      const token = localStorage.getItem('pairsona_token');
      if (!token) return;

      const formData = new FormData();
      Array.from(files).forEach(file => {
        formData.append('files', file);
      });

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/profile/photos`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload photos');
      }

      const result = await response.json();
      if (profile) {
        // Refresh the entire profile to get the updated photos from server
        await fetchProfile();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to upload photos');
    } finally {
      setUploadingPhotos(false);
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const getReligionIcon = (religion: string) => {
    const religionMap: { [key: string]: string } = {
      'islam': '☪️',
      'christian': '✝️',
      'catholic': '✝️',
      'hindu': '🕉️',
      'buddha': '☸️',
      'other': '🙏'
    };
    return religionMap[religion?.toLowerCase()] || '🙏';
  };

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Profil Saya</h1>
          <p className="text-muted-foreground">Memuat profil Anda...</p>
        </div>
        <div className="grid gap-6 md:grid-cols-3">
          {Array.from({ length: 3 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error || !profile) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Profil Saya</h1>
          <p className="text-muted-foreground">Kelola informasi pribadi dan preferensi Anda.</p>
        </div>
        <Card className="p-6">
          <div className="text-center">
            <p className="text-red-600 mb-4">{error || 'Gagal memuat profil'}</p>
            <Button onClick={fetchProfile}>Coba Lagi</Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <X className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
              <div className="ml-auto pl-3">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setError(null)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Profil Saya</h1>
          <p className="text-muted-foreground">Kelola informasi pribadi dan preferensi Anda.</p>
        </div>
        <div className="flex gap-2">
          {editing && (
            <Button
              variant="outline"
              onClick={() => {
                setEditing(false);
                fetchProfile(); // Reset changes
              }}
            >
              Cancel
            </Button>
          )}
          <Button
            className={editing ? "bg-green-600 hover:bg-green-700 text-white" : "bg-[#D0544D] hover:bg-[#D0544D]/90 text-white"}
            onClick={editing ? handleSave : () => setEditing(true)}
            disabled={saving}
          >
            {editing ? (
              <>
                <Save className="mr-2 h-4 w-4" />
                {saving ? 'Menyimpan...' : 'Simpan Perubahan'}
              </>
            ) : (
              <>
                <Edit className="mr-2 h-4 w-4" />
                Edit Profil
              </>
            )}
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-1">
        {/* Combined Profile Information */}
        <Card>
          <CardHeader>
            <CardTitle>Informasi Profil</CardTitle>
            <CardDescription>Kelola detail pribadi dan foto profil Anda</CardDescription>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-8">
              {/* Profile Picture & Summary Section */}
              <div className="border-b border-gray-200 pb-6">
                <h4 className="text-base font-semibold text-gray-900 mb-4">Foto Profil & Ringkasan</h4>
                <div className="flex flex-col md:flex-row gap-6">
                  {/* Profile Picture */}
                  <div className="flex flex-col items-center">
                    <div className="relative mb-4">
                      <Avatar className="w-32 h-32">
                        <AvatarImage src={profile.image} alt={profile.name} />
                        <AvatarFallback className="bg-[#D0544D]/20 text-[#D0544D] text-2xl font-bold">
                          {getInitials(profile.name)}
                        </AvatarFallback>
                      </Avatar>
                      <Button
                        size="sm"
                        className="absolute bottom-0 right-0 rounded-full w-8 h-8 p-0"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={uploadingImage}
                      >
                        {uploadingImage ? (
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        ) : (
                          <Camera className="w-4 h-4" />
                        )}
                      </Button>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) handleImageUpload(file);
                        }}
                      />
                    </div>
                    <div className="text-center">
                      <h3 className="text-lg font-bold">{profile.name}</h3>
                      {profile.dateOfBirth && (
                        <p className="text-sm text-gray-600">
                          {calculateAge(profile.dateOfBirth)} tahun
                        </p>
                      )}
                      <div className="flex items-center gap-2 mt-2 justify-center">
                        {profile.religion && (
                          <Badge variant="secondary" className="text-xs">
                            {getReligionIcon(profile.religion)} {profile.religion}
                          </Badge>
                        )}
                        {profile.occupation && (
                          <Badge variant="outline" className="text-xs">
                            {profile.occupation}
                          </Badge>
                        )}
                      </div>
                      <div className="mt-3">
                        <Badge variant={profile.psychTestCompleted ? "default" : "secondary"} className="text-xs">
                          {profile.psychTestCompleted ? "Profil Lengkap" : "Profil Belum Lengkap"}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {/* Basic Information */}
                  <div className="flex-1">
                    <h5 className="text-sm font-semibold text-gray-900 mb-3">Informasi Dasar</h5>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">Nama Lengkap</label>
                        <Input
                          disabled={!editing}
                          value={profile.name}
                          onChange={(e) => setProfile({ ...profile, name: e.target.value })}
                          className="w-full"
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">Email</label>
                        <Input
                          type="email"
                          disabled={true}
                          value={profile.email}
                          className="w-full bg-gray-50"
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">Nomor Telepon</label>
                        <Input
                          type="tel"
                          disabled={true}
                          value={profile.phoneNumber || 'Tidak tersedia'}
                          className="w-full bg-gray-50"
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">Tanggal Lahir</label>
                        <Input
                          type="date"
                          disabled={!editing}
                          value={profile.dateOfBirth ? new Date(profile.dateOfBirth).toISOString().split('T')[0] : ''}
                          onChange={(e) => setProfile({ ...profile, dateOfBirth: e.target.value })}
                          className="w-full"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Personal Details Section */}
              <div className="border-b border-gray-200 pb-6">
                <h4 className="text-base font-semibold text-gray-900 mb-4">Detail Pribadi</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Jenis Kelamin</label>
                    <Select
                      disabled={!editing}
                      value={profile.gender || ''}
                      onValueChange={(value) => setProfile({ ...profile, gender: value })}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Pilih jenis kelamin" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="male">Laki-laki</SelectItem>
                        <SelectItem value="female">Perempuan</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Agama</label>
                    <Select
                      disabled={!editing}
                      value={profile.religion || ''}
                      onValueChange={(value) => setProfile({ ...profile, religion: value })}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Pilih agama" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="islam">Islam</SelectItem>
                        <SelectItem value="christian">Kristen</SelectItem>
                        <SelectItem value="catholic">Katolik</SelectItem>
                        <SelectItem value="hindu">Hindu</SelectItem>
                        <SelectItem value="buddha">Buddha</SelectItem>
                        <SelectItem value="other">Lainnya</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Pekerjaan</label>
                    <Input
                      disabled={!editing}
                      value={profile.occupation || ''}
                      onChange={(e) => setProfile({ ...profile, occupation: e.target.value })}
                      placeholder="Pekerjaan Anda"
                      className="w-full"
                    />
                  </div>
                </div>
              </div>

              {/* Preferences Section */}
              <div className="border-b border-gray-200 pb-6">
                <h4 className="text-base font-semibold text-gray-900 mb-4">Preferensi</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Status Merokok</label>
                    <Select
                      disabled={!editing}
                      value={profile.isSmoker ? 'yes' : 'no'}
                      onValueChange={(value) => setProfile({ ...profile, isSmoker: value === 'yes' })}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="no">🚭 Tidak merokok</SelectItem>
                        <SelectItem value="yes">🚬 Merokok</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Penerimaan Agama Berbeda</label>
                    <Select
                      disabled={!editing}
                      value={profile.acceptDifferentReligion ? 'yes' : 'no'}
                      onValueChange={(value) => setProfile({ ...profile, acceptDifferentReligion: value === 'yes' })}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="yes">🤝 Menerima agama berbeda</SelectItem>
                        <SelectItem value="no">🙏 Lebih suka agama yang sama</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* About Me Section */}
              <div>
                <h4 className="text-base font-semibold text-gray-900 mb-4">Tentang Saya</h4>
                <div className="space-y-2">
                  <Textarea
                    disabled={!editing}
                    value={profile.about || ''}
                    onChange={(e) => setProfile({ ...profile, about: e.target.value })}
                    placeholder="Ceritakan tentang diri Anda..."
                    className="w-full h-24 resize-none"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Photos Gallery */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Galeri Foto</span>
              <Button
                size="sm"
                variant="outline"
                onClick={() => photosInputRef.current?.click()}
                disabled={uploadingPhotos || profile.photos.length >= 5}
              >
                {uploadingPhotos ? (
                  <>
                    <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin mr-2" />
                    Mengunggah...
                  </>
                ) : (
                  <>
                    <Plus className="w-4 h-4 mr-2" />
                    Tambah Foto ({profile.photos.length}/5)
                  </>
                )}
              </Button>
              <input
                ref={photosInputRef}
                type="file"
                accept="image/*"
                multiple
                className="hidden"
                onChange={(e) => {
                  const files = e.target.files;
                  if (files && files.length > 0) {
                    const remainingSlots = 5 - profile.photos.length;
                    if (files.length > remainingSlots) {
                      setError(`Anda hanya dapat mengunggah ${remainingSlots} foto lagi`);
                      return;
                    }
                    handlePhotosUpload(files);
                  }
                }}
              />
            </CardTitle>
            <CardDescription>Unggah hingga 5 foto untuk menampilkan diri Anda (saat ini: {profile.photos.length}/5)</CardDescription>
          </CardHeader>
          <CardContent>
            {profile.photos.length === 0 ? (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                <Camera className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Belum ada foto</h3>
                <p className="text-gray-500 mb-4">Unggah beberapa foto untuk membuat profil Anda lebih menarik!</p>
                <Button
                  onClick={() => photosInputRef.current?.click()}
                  disabled={uploadingPhotos}
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Unggah Foto
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                {profile.photos.map((photo, index) => (
                  <div key={photo.id} className="relative group">
                    <div className="aspect-square rounded-lg overflow-hidden border border-gray-300">
                      <img
                        src={photo.url}
                        alt={`Photo ${index + 1}`}
                        className="w-full h-full object-cover"
                        style={{ display: 'block' }}
                      />
                    </div>
                    <div className="absolute top-2 right-2">
                      <Button
                        size="sm"
                        variant="destructive"
                        className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0"
                        onClick={() => {
                          // Note: You might want to implement photo deletion API
                          setProfile({
                            ...profile,
                            photos: profile.photos.filter(p => p.id !== photo.id)
                          });
                        }}
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>


      </div>
    </div>
  );
}
